# دليل إعداد البريد الإلكتروني - Email Setup Guide

## المشكلة الحالية - Current Issue
```
Connection could not be established with host "sandbox.smtp.mailtrap.io:2525"
```

## الحلول المتاحة - Available Solutions

### الحل الأول: استخدام Log Driver (الأسرع)
هذا الحل يحفظ البريد الإلكتروني في ملف log بدلاً من إرساله فعلياً.

في ملف `.env`:
```
MAIL_MAILER=log
```

البريد الإلكتروني سيُحفظ في: `storage/logs/laravel.log`

### الحل الثاني: استخدام Gmail SMTP
لإرسال بريد إلكتروني حقيقي عبر Gmail:

1. **إنشاء App Password في Gmail:**
   - اذه<PERSON> <PERSON>لى Google Account Settings
   - Security → 2-Step Verification
   - App passwords → Generate new password

2. **تحديث ملف `.env`:**
```
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

### الحل الثالث: إعداد Mailtrap جديد
1. اذهب إلى [mailtrap.io](https://mailtrap.io)
2. أنشئ حساب جديد
3. احصل على إعدادات SMTP الجديدة
4. حدث ملف `.env`:

```
MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-new-username
MAIL_PASSWORD=your-new-password
MAIL_ENCRYPTION=null
```

## التحسينات المضافة - Added Improvements

### 1. معالجة أخطاء البريد الإلكتروني
- تم إضافة `MailException` class لمعالجة أخطاء البريد
- تم تحديث جميع Jobs لمعالجة الأخطاء
- الأخطاء تُسجل في logs ولا توقف العملية

### 2. تسجيل العمليات - Logging
- تسجيل نجاح إرسال البريد
- تسجيل فشل إرسال البريد مع التفاصيل
- سهولة تتبع مشاكل البريد الإلكتروني

### 3. عدم توقف العمليات
- فشل إرسال البريد لا يوقف عملية الطلب
- المستخدم يمكنه إكمال الطلب حتى لو فشل البريد

## اختبار الإعداد - Testing Setup

بعد تحديث إعدادات البريد، اختبر بـ:

```bash
php artisan tinker
```

```php
Mail::raw('Test email', function ($message) {
    $message->to('<EMAIL>')->subject('Test');
});
```

## ملاحظات مهمة - Important Notes

1. **للتطوير**: استخدم `log` driver
2. **للإنتاج**: استخدم Gmail أو خدمة بريد موثوقة
3. **Mailtrap**: مفيد للاختبار فقط
4. **تأكد من**: تشغيل `php artisan config:cache` بعد تغيير الإعدادات
